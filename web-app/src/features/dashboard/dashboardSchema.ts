import * as yup from 'yup';
import { ChartType, RollingPeriod, ChartOrientation } from '../chart/chartTypes';
import { StatsGroupBy, StatsModule } from '../stats/statsTypes';

// Define the chart settings schema that can be reused across components
export const chartSettingsSchema = yup.object({
  id: yup.string().required('Please provide a chart ID'),
  name: yup.string().required('Please enter a name for your chart'),
  module: yup
    .mixed<StatsModule>()
    .nullable()
    .required('Please select a data module')
    .oneOf([...Object.values(StatsModule)], 'Please select a valid data module'),
  type: yup
    .mixed<ChartType>()
    .nullable()
    .required('Please select a chart type')
    .oneOf([...Object.values(ChartType)], 'Please select a valid chart type'),
  layouts: yup.object().required().label('Layouts'),
  fieldColorMapping: yup.object().required().label('Field color mapping'),
  groupBy: yup
    .mixed<StatsGroupBy>()
    .nullable()
    .when('type', {
      is: (val: ChartType) => val !== ChartType.LINE,
      then: yup
        .mixed<StatsGroupBy>()
        .nullable()
        .required('Please select how to group your data')
        .oneOf([...Object.values(StatsGroupBy)], 'Please select a valid grouping option'),
      otherwise: yup
        .mixed()
        .nullable()
        .oneOf([...Object.values(StatsGroupBy), null], 'Please select a valid grouping option'),
    }),
  rollingPeriod: yup
    .mixed<RollingPeriod>()
    .nullable()
    .required('Please select a time period for your chart')
    .oneOf([...Object.values(RollingPeriod)], 'Please select a valid time period'),
  orientation: yup
    .mixed<ChartOrientation>()
    .nullable()
    .when('type', {
      is: ChartType.BAR,
      then: yup
        .mixed<ChartOrientation>()
        .required('Please select a chart orientation')
        .oneOf([...Object.values(ChartOrientation)], 'Please select a valid orientation'),
      otherwise: yup.mixed().nullable(),
    }),
  stackedBy: yup
    .mixed<StatsGroupBy>()
    .nullable()
    .when(['type', 'groupBy'], {
      is: (type: ChartType, groupBy: StatsGroupBy | null) => type === ChartType.BAR && !!groupBy,
      then: yup
        .mixed<StatsGroupBy>()
        .nullable()
        .oneOf([...Object.values(StatsGroupBy), null], 'Please select a valid stacking option'),
      otherwise: yup.mixed().nullable(),
    }),
  valueColorRanges: yup
    .array()
    .of(
      yup.object({
        minValue: yup.number().required('Minimum value is required'),
        maxValue: yup.number().required('Maximum value is required'),
        color: yup.string().required('Color is required'),
      })
    )
    .nullable()
    .test('no-overlapping-ranges', 'Color ranges cannot overlap', function validateRanges(ranges) {
      if (!ranges || ranges.length === 0) return true;

      const sortedRanges = [...ranges].sort((a, b) => (a.minValue || 0) - (b.minValue || 0));

      for (let i = 0; i < sortedRanges.length; i += 1) {
        const range = sortedRanges[i];

        if ((range.minValue || 0) >= (range.maxValue || 0)) {
          return this.createError({
            message: `Range ${i + 1}: Minimum value must be less than maximum value`,
            path: `${this.path}[${i}]`,
          });
        }

        if (i < sortedRanges.length - 1) {
          const nextRange = sortedRanges[i + 1];
          if ((range.maxValue || 0) > (nextRange.minValue || 0)) {
            return this.createError({
              message: `Ranges ${i + 1} and ${i + 2} overlap`,
              path: `${this.path}[${i}]`,
            });
          }
        }
      }

      return true;
    }),
});

export const dashboardSchema = yup.object({
  name: yup.string().required().label('Dashboard name'),
  description: yup.string().required().label('Dashboard description'),
  group: yup
    .object({
      id: yup.number().required(),
      name: yup.string().required(),
    })
    .required()
    .label('Group'),
  settings: yup.array().of(chartSettingsSchema),
});
