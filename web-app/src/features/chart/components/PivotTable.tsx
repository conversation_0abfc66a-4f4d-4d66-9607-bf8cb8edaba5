import { useMemo, useCallback } from 'react';
import { ResponsiveHeatMap } from '@nivo/heatmap';
import { Box, Typography } from '@mui/material';
import { StatsKeyValue, StatsGroupBy } from '../../stats/statsTypes';
import { getFieldName, getValueBasedColor } from '../chartFunctions';
import { ValueColorRange } from '../chartTypes';

interface TooltipProps {
  cell: {
    serieId: string;
    data: { x: string };
    formattedValue: string | null;
  };
}

function HeatmapTooltip({ cell }: TooltipProps) {
  return <Box
    sx={{
      background: 'white',
      padding: '9px 12px',
      border: '1px solid #ccc',
      borderRadius: '4px',
      boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
    }}
  >
    <Typography variant="body2">
      <strong>{cell.serieId}</strong> × <strong>{cell.data.x}</strong>
    </Typography>
    <Typography variant="body2" color="primary">
      Count: <strong>{cell.formattedValue || '0'}</strong>
    </Typography>
  </Box>
}

interface PivotTableProps {
  data: StatsKeyValue;
  fieldGroupByNamingMapping: Record<string, string>;
  fieldStackedByNamingMapping: Record<string, string>;
  groupBy: StatsGroupBy;
  stackedBy: StatsGroupBy;
  valueColorRanges?: ValueColorRange[];
}

interface PivotData {
  rows: string[];
  columns: string[];
  values: Record<string, Record<string, number>>;
  totals: {
    rowTotals: Record<string, number>;
    columnTotals: Record<string, number>;
    grandTotal: number;
  };
  maxValue: number;
}

function PivotTable({
  data,
  fieldGroupByNamingMapping,
  fieldStackedByNamingMapping,
  groupBy,
  stackedBy,
  valueColorRanges,
}: PivotTableProps) {
  const pivotData = useMemo((): PivotData => {
    const rows = new Set<string>();
    const columns = new Set<string>();
    const values: Record<string, Record<string, number>> = {};
    const rowTotals: Record<string, number> = {};
    const columnTotals: Record<string, number> = {};
    let grandTotal = 0;
    let maxValue = 0;

    // Process the nested data structure
    Object.entries(data).forEach(([rowKey, rowValue]) => {
      if (typeof rowValue === 'object' && !('count' in rowValue)) {
        rows.add(rowKey);
        if (!values[rowKey]) values[rowKey] = {};
        if (!rowTotals[rowKey]) rowTotals[rowKey] = 0;

        Object.entries(rowValue).forEach(([colKey, colValue]) => {
          if (typeof colValue === 'object' && 'count' in colValue) {
            columns.add(colKey);
            const { count } = colValue as { count: number };
            values[rowKey][colKey] = count;
            rowTotals[rowKey] += count;
            columnTotals[colKey] = (columnTotals[colKey] || 0) + count;
            grandTotal += count;
            maxValue = Math.max(maxValue, count);
          }
        });
      }
    });

    return {
      rows: Array.from(rows).sort(),
      columns: Array.from(columns).sort(),
      values,
      totals: { rowTotals, columnTotals, grandTotal },
      maxValue,
    };
  }, [data]);

  const getDisplayName = useCallback((key: string, type: 'row' | 'column') => {
    if (type === 'row') {
      return fieldGroupByNamingMapping[key];
    }
    return fieldStackedByNamingMapping[key];
  }, [fieldGroupByNamingMapping, fieldStackedByNamingMapping]);

  // Transform data for Nivo heatmap
  const heatmapData = useMemo(() =>
    pivotData.rows.map((row) => ({
      id: getDisplayName(row, 'row'),
      data: pivotData.columns.map((col) => ({
        x: getDisplayName(col, 'column'),
        y: pivotData.values[row]?.[col] || 0,
      })),
    }))
  , [pivotData, getDisplayName]);

  // Custom color function for value-based coloring
  const getCustomColor = useCallback((cell: { value: number | null }) => {
    if (valueColorRanges && valueColorRanges.length > 0 && cell.value !== null) {
      return getValueBasedColor(cell.value, valueColorRanges);
    }
    // Fallback to default color
    return '#3182ce';
  }, [valueColorRanges]);

  // Determine colors configuration
  const colorsConfig = useMemo(() => {
    if (valueColorRanges && valueColorRanges.length > 0) {
      return getCustomColor;
    }
    return {
      type: 'sequential' as const,
      scheme: 'blues' as const,
      minValue: 0,
      maxValue: pivotData.maxValue,
    };
  }, [valueColorRanges, getCustomColor, pivotData.maxValue]);

  if (pivotData.rows.length === 0 || pivotData.columns.length === 0) {
    return (
      <Box sx={{ p: 2, textAlign: 'center' }}>
        <Typography color="text.secondary">No data available for pivot table</Typography>
      </Box>
    );
  }

  return (
    <Box sx={{ height: '100%', position: 'relative' }}>
      <ResponsiveHeatMap
        data={heatmapData}
        margin={{ top: 60, right: 90, bottom: 60, left: 90 }}
        valueFormat=">-.0f"
        axisTop={null}
        axisRight={null}
        axisBottom={{
          tickSize: 5,
          tickPadding: 5,
          tickRotation: -45,
          legend: getFieldName('', stackedBy) || 'Columns',
          legendPosition: 'middle',
          legendOffset: 46,
        }}
        axisLeft={{
          tickSize: 5,
          tickPadding: 5,
          tickRotation: 0,
          legend: getFieldName('', groupBy) || 'Rows',
          legendPosition: 'middle',
          legendOffset: -72,
        }}
        borderColor={{
          from: 'color',
          modifiers: [['darker', 0.4]],
        }}
        labelTextColor={{
          from: 'color',
          modifiers: [['darker', 1.8]],
        }}
        animate
        motionConfig="wobbly"
        tooltip={HeatmapTooltip}
        colors={colorsConfig}
      />

      {/* Summary information overlay */}
      {/* <Box
        sx={{
          position: 'absolute',
          top: 8,
          right: 8,
          background: 'rgba(255, 255, 255, 0.9)',
          padding: '8px 12px',
          borderRadius: '4px',
          border: '1px solid #e0e0e0',
          fontSize: '0.875rem',
        }}
      >
        <Typography variant="caption" display="block">
          <strong>Total: {pivotData.totals.grandTotal}</strong>
        </Typography>
        <Typography variant="caption" display="block" color="text.secondary">
          {pivotData.rows.length} rows × {pivotData.columns.length} columns
        </Typography>
      </Box> */}
    </Box>
  );
}

export default PivotTable;
