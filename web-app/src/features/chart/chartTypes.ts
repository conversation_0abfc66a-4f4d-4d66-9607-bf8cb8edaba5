import { Layout } from 'react-grid-layout';
import { StatsGroupBy, StatsModule } from '../stats/statsTypes';
import { ThemeColor } from '../../theme';

export const paletteColors = [
  'primary.main',
  'secondary.main',
  'success.main',
  'success.dark',
  'error.main',
  'error.light',
  'error.dark',
  'warning.main',
  'warning.light',
] as ThemeColor[];

export enum ChartType {
  PIE = 'PIE',
  BAR = 'BAR',
  LINE = 'LINE',
  PIVOT_TABLE = 'PIVOT_TABLE',
}

export const ChartTypeDisplayMap: Record<ChartType, string> = {
  [ChartType.PIE]: 'Pie Chart',
  [ChartType.BAR]: 'Bar Chart',
  [ChartType.LINE]: 'Line Chart',
  [ChartType.PIVOT_TABLE]: 'Pivot Table',
};

export enum ChartOrientation {
  HORIZONTAL = 'HORIZONTAL',
  VERTICAL = 'VERTICAL',
}

export const ChartOrientationDisplayMap: Record<ChartOrientation, string> = {
  [ChartOrientation.HORIZONTAL]: 'Horizontal',
  [ChartOrientation.VERTICAL]: 'Vertical',
};

export interface ValueColorRange {
  minValue: number;
  maxValue: number;
  color: string;
}

export interface ChartSetting {
  id: string;
  name: string;
  type: ChartType;
  module: StatsModule;
  groupBy: StatsGroupBy;
  stackedBy?: StatsGroupBy;
  rollingPeriod: RollingPeriod;
  layouts: Record<string, Layout>;
  fieldColorMapping?: Record<string, string>;
  orientation?: ChartOrientation;
  valueColorRanges?: ValueColorRange[];
}

export enum RollingPeriod {
  LAST_7_DAYS = 'LAST_7_DAYS',
  LAST_30_DAYS = 'LAST_30_DAYS',
  LAST_90_DAYS = 'LAST_90_DAYS',
  LAST_6_MONTHS = 'LAST_6_MONTHS',
  LAST_YEAR = 'LAST_YEAR',
}

export const RollingPeriodDisplay: Record<RollingPeriod, string> = {
  [RollingPeriod.LAST_7_DAYS]: 'Last 7 Days',
  [RollingPeriod.LAST_30_DAYS]: 'Last 30 Days',
  [RollingPeriod.LAST_90_DAYS]: 'Last 90 Days',
  [RollingPeriod.LAST_6_MONTHS]: 'Last 6 Months',
  [RollingPeriod.LAST_YEAR]: 'Last Year',
};

export interface ChartConfigurationFormInput {
  id: string;
  name: string;
  fieldArrayId?: number;
  module: StatsModule | null;
  type: ChartType | null;
  groupBy?: StatsGroupBy | null;
  stackedBy?: StatsGroupBy | null;
  rollingPeriod?: RollingPeriod | null;
  orientation?: ChartOrientation | null;
  layouts?: Record<string, Layout>;
  fieldColorMapping?: Record<string, string>;
  valueColorRanges?: ValueColorRange[];
}

export const isStackedByChart = (chartType: ChartType) => chartType === ChartType.BAR || chartType === ChartType.LINE || chartType === ChartType.PIVOT_TABLE;
