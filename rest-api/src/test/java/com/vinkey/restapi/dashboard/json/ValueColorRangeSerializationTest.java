package com.vinkey.restapi.dashboard.json;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

public class ValueColorRangeSerializationTest {

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Test
    public void testValueColorRangeSerialization() throws Exception {
        // Create a ValueColorRange object
        ValueColorRange range = new ValueColorRange();
        range.setMinValue(0.0);
        range.setMaxValue(10.0);
        range.setColor("#ff0000");

        // Serialize to JSON
        String json = objectMapper.writeValueAsString(range);
        
        // Verify JSON contains expected fields
        assertTrue(json.contains("\"minValue\":0.0"));
        assertTrue(json.contains("\"maxValue\":10.0"));
        assertTrue(json.contains("\"color\":\"#ff0000\""));
    }

    @Test
    public void testValueColorRangeDeserialization() throws Exception {
        // JSON string representing a ValueColorRange
        String json = "{\"minValue\":5.5,\"maxValue\":15.5,\"color\":\"#00ff00\"}";
        
        // Deserialize from JSON
        ValueColorRange range = objectMapper.readValue(json, ValueColorRange.class);
        
        // Verify deserialized object
        assertEquals(5.5, range.getMinValue());
        assertEquals(15.5, range.getMaxValue());
        assertEquals("#00ff00", range.getColor());
    }

    @Test
    public void testChartSettingWithValueColorRanges() throws Exception {
        // Create ChartSetting with valueColorRanges
        ChartSetting chartSetting = new ChartSetting();
        chartSetting.setId("test-chart");
        chartSetting.setName("Test Chart");
        
        // Create value color ranges
        ValueColorRange range1 = new ValueColorRange();
        range1.setMinValue(0.0);
        range1.setMaxValue(10.0);
        range1.setColor("#ff0000");
        
        ValueColorRange range2 = new ValueColorRange();
        range2.setMinValue(10.0);
        range2.setMaxValue(20.0);
        range2.setColor("#00ff00");
        
        List<ValueColorRange> ranges = Arrays.asList(range1, range2);
        chartSetting.setValueColorRanges(ranges);
        
        // Serialize to JSON
        String json = objectMapper.writeValueAsString(chartSetting);
        
        // Verify JSON structure
        assertTrue(json.contains("\"valueColorRanges\""));
        assertTrue(json.contains("\"minValue\":0.0"));
        assertTrue(json.contains("\"maxValue\":10.0"));
        assertTrue(json.contains("\"color\":\"#ff0000\""));
        
        // Deserialize back
        ChartSetting deserializedSetting = objectMapper.readValue(json, ChartSetting.class);
        
        // Verify deserialized object
        assertNotNull(deserializedSetting.getValueColorRanges());
        assertEquals(2, deserializedSetting.getValueColorRanges().size());
        assertEquals("#ff0000", deserializedSetting.getValueColorRanges().get(0).getColor());
        assertEquals("#00ff00", deserializedSetting.getValueColorRanges().get(1).getColor());
    }

    @Test
    public void testChartSettingWithNullValueColorRanges() throws Exception {
        // Create ChartSetting without valueColorRanges
        ChartSetting chartSetting = new ChartSetting();
        chartSetting.setId("test-chart");
        chartSetting.setName("Test Chart");
        chartSetting.setValueColorRanges(null);
        
        // Serialize to JSON
        String json = objectMapper.writeValueAsString(chartSetting);
        
        // With @JsonInclude(JsonInclude.Include.NON_NULL), null fields should not appear
        assertFalse(json.contains("\"valueColorRanges\""));
        
        // Deserialize back
        ChartSetting deserializedSetting = objectMapper.readValue(json, ChartSetting.class);
        
        // Verify null handling
        assertNull(deserializedSetting.getValueColorRanges());
    }
}
