package com.vinkey.restapi.dashboard.json;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.fasterxml.jackson.annotation.JsonTypeInfo.As;
import com.fasterxml.jackson.annotation.JsonTypeInfo.Id;
import com.vinkey.restapi.dashboard.ChartTypeEnum;
import com.vinkey.restapi.stats.TimeSerieModules;
import com.vinkey.restapi.stats.dto.GroupByEnum;
import java.io.Serializable;
import java.util.Map;

@JsonTypeInfo(use = Id.NAME, include = As.PROPERTY, property = "type", visible = true)
@JsonSubTypes({
  @JsonSubTypes.Type(value = ChartSetting.class, name = "PIE"),
  @JsonSubTypes.Type(value = BarChartSetting.class, name = "BAR"),
  @JsonSubTypes.Type(value = StackedByChartSetting.class, name = "LINE"),
  @JsonSubTypes.Type(value = HeatMapChartSetting.class, name = "PIVOT_TABLE")
})
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ChartSetting implements Serializable {
  private ChartTypeEnum type;
  private String id;
  private String name;
  private TimeSerieModules module;
  private Map<String, LayoutItem> layouts;
  private Map<String, String> fieldColorMapping;
  private GroupByEnum groupBy;
  private String rollingPeriod;

  public String getId() {
    return id;
  }

  public void setId(String id) {
    this.id = id;
  }

  public String getName() {
    return name;
  }

  public void setName(String name) {
    this.name = name;
  }

  public Map<String, String> getFieldColorMapping() {
    return fieldColorMapping;
  }

  public void setFieldColorMapping(Map<String, String> fieldColorMapping) {
    this.fieldColorMapping = fieldColorMapping;
  }

  public TimeSerieModules getModule() {
    return module;
  }

  public void setModule(TimeSerieModules module) {
    this.module = module;
  }

  public ChartTypeEnum getType() {
    return type;
  }

  public void setType(ChartTypeEnum type) {
    this.type = type;
  }

  public Map<String, LayoutItem> getLayouts() {
    return layouts;
  }

  public void setLayouts(Map<String, LayoutItem> layouts) {
    this.layouts = layouts;
  }

  public GroupByEnum getGroupBy() {
    return groupBy;
  }

  public void setGroupBy(GroupByEnum groupBy) {
    this.groupBy = groupBy;
  }

  public String getRollingPeriod() {
    return rollingPeriod;
  }

  public void setRollingPeriod(String rollingPeriod) {
    this.rollingPeriod = rollingPeriod;
  }
}
